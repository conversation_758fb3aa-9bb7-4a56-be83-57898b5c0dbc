<template>
    <view v-if="!warehouseLocationList.length">
        <nut-empty :description="t('helper.noData')"></nut-empty>
    </view>
    <view v-else>
        <view style="margin-top: -20px; margin-bottom: 8px">
            <nut-row>
                <nut-col :span="12">
                    <nut-button type="info" plain size="small" @click="addVisible = true">
                        {{ t('ui.add') }}
                    </nut-button>
                </nut-col>
                <nut-col :span="12" style="text-align: right">
                    <nut-button type="info" plain size="small" @click="fetchData">
                        {{ t('ui.refresh') }}
                    </nut-button>
                </nut-col>
            </nut-row>
        </view>
        <InformationCard
            v-for="(item, index) in warehouseLocationList"
            :key="index"
            :show-empty-image="false"
            :isActivate="item.isActivate"
            @click:content="handleDetails(item)"
        >
            <template #front-title> [{{ index + 1 }}] </template>
            <template #title>{{ item.name }}</template>
            <template #line-first>{{ t('text.encode') }}: {{ item.encode }}</template>
            <template #line-second>{{ t('text.location') }}: {{ item.location }}</template>
            <template #line-third>{{ t('text.describe') }}: {{ item.describe }}</template>
            <template #line-fourth>{{ t('text.dutyUser') }}: {{ item.dutyUserName || '-' }}</template>
            <template #space-one>
                <nut-tag :type="item.isActivate ? 'primary' : 'danger'" size="small">{{
                    item.isActivate ? t('tag.enable') : t('tag.false')
                }}</nut-tag>
            </template>
            <template #space-two>
                <view @click.stop>
                    <action-menu
                        :options="actionOptions"
                        @select="(value, option) => handleActionSelect(option, item)"
                        direction="left"
                    ></action-menu>
                </view>
            </template>
        </InformationCard>
        <nut-popup v-model:visible="addVisible" :style="{ padding: '16px', height: '85%', width: '92%' }" round>
            <warehouse-location-add
                :addVisible="addVisible"
                @update:addVisible="addVisible = $event"
                :areaId="locationId"
                @refresh="fetchData"
            />
        </nut-popup>
        <nut-popup v-model:visible="editVisible" :style="{ padding: '16px', height: '85%', width: '92%' }" round>
            <warehouse-location-edit
                :editVisible="editVisible"
                @update:editVisible="editVisible = $event"
                :id="editId"
                @refresh="fetchData"
            />
        </nut-popup>
        <nut-dialog
            :content="t('text.isDelete')"
            v-model:visible="deleteVisible"
            @cancel="handleDeleteConfirm(false)"
            @ok="handleDeleteConfirm(true)"
        />
    </view>
</template>

<script setup lang="ts">
import Taro from '@tarojs/taro'
import { t } from '@/locale/fanyi'
import { ref, watch } from 'vue'
import { WmWarehouseLocationService } from '@/api/proxy/enterprise/controller/wm-warehouse-location.service'
import { GetWmWarehouseLocationInput, WmWarehouseLocationListDto } from '@/api/proxy/enterprise/wms/dtos/models'
import { IdentityUserService } from '@/api/proxy/identity-user-service/identity-user-service.service'
import { GetIdentityUsersInput } from '@/api/proxy/identity-user-service/models'
import InformationCard from '@/components/InformationCard.vue'
import WarehouseLocationAdd from './warehouse-location-add.vue'
import { ActionMenuItem } from '@/components/ActionMenu/models/ActionMenuDto'
import WarehouseLocationEdit from './warehouse-location-edit.vue'
import { Loading, HideLoading } from '@/utils/Taro-api'

Taro.setNavigationBarTitle({ title: t('menu.warehouseLocation') })

interface ExtendedWmWarehouseLocationListDto extends WmWarehouseLocationListDto {
    dutyUserName?: string
}

const props = defineProps<{
    locationId?: string
}>()

const locationId = ref(props.locationId)
const wmWarehouseLocationService = new WmWarehouseLocationService()
const identityUserService = new IdentityUserService()
const warehouseLocationList = ref<ExtendedWmWarehouseLocationListDto[]>([])
const addVisible = ref<boolean>(false)
const editVisible = ref<boolean>(false)
const editId = ref<string>('')
const deleteVisible = ref<boolean>(false)
const deleteId = ref<string>('')
const actionOptions = ref<ActionMenuItem[]>([
    { text: t('menu.edit'), value: 'edit' },
    { text: t('menu.delete'), value: 'delete' },
])

// 批量获取用户信息
const fetchUserInfo = async (locationList: WmWarehouseLocationListDto[]) => {
    try {
        const userIds = [
            ...new Set(locationList.map(item => item.dutyUserId).filter((id): id is string => Boolean(id))),
        ]
        if (userIds.length === 0) return locationList
        const input: GetIdentityUsersInput = {
            maxResultCount: 1000,
            skipCount: 0,
        }
        const allUsersResult = await identityUserService.getList(input)
        const userMap = new Map<string, string>()
        if (allUsersResult && allUsersResult.items) {
            allUsersResult.items.forEach(user => {
                if (user.id) {
                    const fullName = (user.surname || '') + (user.name || '')
                    userMap.set(user.id, fullName || user.email || '-')
                }
            })
        }
        // 将用户姓名映射到库位数据
        return locationList.map(item => ({
            ...item,
            dutyUserName: item.dutyUserId ? userMap.get(item.dutyUserId) || '-' : '-',
        }))
    } catch (error) {
        console.error('获取用户信息失败', error)
        return locationList.map(item => ({ ...item, dutyUserName: '-' }))
    }
}

const fetchData = async () => {
    const input: GetWmWarehouseLocationInput = {
        areaId: locationId.value,
        skipCount: 0,
        maxResultCount: 1000,
    }
    try {
        Loading()
        const result = await wmWarehouseLocationService.getPaged(input)
        const extendedList = await fetchUserInfo(result.items)
        warehouseLocationList.value = extendedList
        HideLoading()
    } catch (error) {
        console.error('获取仓库库位数据失败', error)
        HideLoading()
    }
}

const handleDeleteConfirm = (confirmed: boolean) => {
    if (confirmed && deleteId.value) {
        executeDelete(deleteId.value)
    }
    deleteVisible.value = false
    deleteId.value = ''
}

const executeDelete = async (id: string) => {
    try {
        await wmWarehouseLocationService.deleteById(id)
        await fetchData()
        Taro.showToast({
            title: t('text.deleteSuccess'),
            icon: 'success',
        })
    } catch (error) {
        console.error('删除仓库库位数据失败', error)
    }
}

const handleActionSelect = (action: ActionMenuItem, item: ExtendedWmWarehouseLocationListDto) => {
    switch (action.value) {
        case 'edit':
            editId.value = item.id
            editVisible.value = true
            break
        case 'delete':
            deleteId.value = item.id
            deleteVisible.value = true
            break
    }
}

const handleDetails = (item: ExtendedWmWarehouseLocationListDto) => {
    const dutyUserName = encodeURIComponent(item.dutyUserName || '-')
    Taro.navigateTo({
        url: `/pages/workbench/warehouse-management/component/warehouse-location-details?id=${item.id}&dutyUserName=${dutyUserName}`,
    })
}

watch(
    () => props.locationId,
    newValue => {
        locationId.value = newValue
        fetchData()
    },
)
</script>

<style lang="scss"></style>
