<template>
    <view class="purchase-items">
        <view v-if="loading" class="loading-container">
            <nut-skeleton rows="5" title animated />
        </view>
        <view v-else-if="itemsList.length === 0" class="empty-container">
            <nut-empty description="暂无采购子项" image="empty" />
        </view>
        <view v-else class="items-list">
            <view
                v-for="(item, index) in itemsList"
                :key="item.id"
                class="item-container"
                @click="navigateToItemDetails(item.id)"
            >
                <InformationCard>
                    <template #front-title>[{{ index + 1 }}]</template>
                    <template #title>{{ item.name }}</template>
                    <template #line-first>编码：{{ item.encode }}</template>
                    <template #line-second>描述：{{ item.description || '-' }}</template>
                    <template #line-third>
                        <view class="price-row">
                            <text>数量：{{ item.quantity }}</text>
                            <text style="margin-left: 20px">单价：¥{{ item.unitPrice }}</text>
                        </view>
                    </template>
                    <template #content-default>总价：¥{{ item.totalPrice }}</template>
                    <template #space-one>
                        <nut-tag :color="getTypeColor(item.type)" style="margin-bottom: 0.5vh; margin-right: 0.5vh">
                            {{ getTypeText(item.type) }}
                        </nut-tag>
                        <nut-tag :color="getStatusColor(item.status)" style="margin-bottom: 0.5vh">
                            {{ getStatusText(item.status) }}
                        </nut-tag>
                    </template>
                </InformationCard>
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { t } from '@/locale/fanyi'
import { PurchaseItemsService } from '@/api/proxy/enterprise/controller/purchase-items.service'
import { Toast, Loading, HideLoading, NavigateTo } from '@/utils/Taro-api'
import InformationCard from '@/components/InformationCard.vue'

interface PurchaseItem {
    id: string
    name: string
    encode: string
    description: string
    type: 'Material' | 'Temporary'
    materialId: string
    quantity: number
    unitPrice: number
    totalPrice: number
    purchaseOrderId: string
    measureUnitId: string
    categoryId: string
    supplierId?: string
    status: string
}

interface PurchaseItemsProps {
    purchaseOrderId: string
}

const props = defineProps<PurchaseItemsProps>()

const purchaseItemsService = new PurchaseItemsService()
const itemsList = ref<PurchaseItem[]>([])
const loading = ref(false)

// 类型颜色映射
const getTypeColor = (type: string) => {
    switch (type) {
        case 'Material':
            return '#67C23A' // 绿色
        case 'Temporary':
            return '#E6A23C' // 橙色
        default:
            return '#909399' // 灰色
    }
}

// 类型文本映射
const getTypeText = (type: string) => {
    switch (type) {
        case 'Material':
            return '物料'
        case 'Temporary':
            return '临时'
        default:
            return type
    }
}

// 状态颜色映射
const getStatusColor = (status: string) => {
    switch (status) {
        case 'Newly':
            return '#909399' // 灰色
        case 'PendingApproval':
            return '#E6A23C' // 橙色
        case 'Approved':
            return '#67C23A' // 绿色
        case 'Purchasing':
            return '#E6A23C' // 橙色
        case 'Ordered':
            return '#E6A23C' // 橙色
        case 'Rejected':
            return '#F56C6C' // 红色
        case 'Cancelled':
            return '#909399' // 灰色
        case 'Completed':
            return '#67C23A' // 绿色
        case 'Returned':
            return '#F56C6C' // 红色
        default:
            return '#909399' // 灰色
    }
}

// 状态文本映射
const getStatusText = (status: string) => {
    switch (status) {
        case 'Newly':
            return '新建'
        case 'PendingApproval':
            return '待审批'
        case 'Approved':
            return '已审批'
        case 'Purchasing':
            return '采购中'
        case 'Ordered':
            return '已下单'
        case 'Rejected':
            return '已拒绝'
        case 'Cancelled':
            return '已取消'
        case 'Completed':
            return '已完成'
        case 'Returned':
            return '有退回'
        default:
            return status
    }
}

// 获取采购子项数据
const fetchPurchaseItems = async () => {
    if (!props.purchaseOrderId) return

    try {
        loading.value = true
        Loading()

        const result = await purchaseItemsService.getPaged({
            purchaseOrderId: props.purchaseOrderId,
            filterText: '',
            skipCount: 0,
            maxResultCount: 100,
        })

        if (result && result.items) {
            itemsList.value = result.items
        } else {
            itemsList.value = []
        }
    } catch (error) {
        console.error('获取采购子项失败:', error)
        Toast(t('text.errorRequest'), { icon: 'none' })
    } finally {
        loading.value = false
        HideLoading()
    }
}

// 监听采购单ID变化
watch(
    () => props.purchaseOrderId,
    newVal => {
        if (newVal) {
            fetchPurchaseItems()
        } else {
            itemsList.value = []
        }
    },
    { immediate: true },
)

// 暴露刷新方法
defineExpose({
    refresh: fetchPurchaseItems,
})

// 导航到详情页
const navigateToItemDetails = (id: string) => {
    emit('showDetails', id)
}

// 定义事件
const emit = defineEmits(['showDetails'])
</script>

<style lang="scss" scoped>
.purchase-items {
    background-color: #f5f5f5;
    padding: 12px;
    min-height: calc(100vh - 100px);

    .loading-container {
        padding: 20px 0;
    }

    .empty-container {
        padding: 30px 0;
        text-align: center;
    }

    .items-list {
        .item-container {
            margin-bottom: 12px;

            :deep(.information-card) {
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            }
        }

        .price-row {
            display: flex;
            align-items: center;
            color: #606266;

            text:nth-child(2) {
                color: #e6a23c;
                font-weight: 500;
            }
        }
    }
}
</style>
